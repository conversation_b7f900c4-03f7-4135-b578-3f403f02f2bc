using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos.GamificationDtos;
using App.ECommerce.Units.Abstractions.Entities;

namespace App.ECommerce.ProcessFlow.Interface
{
    public interface IGamificationFlow
    {
        Task<Result<List<GameCampaignDto>>> GetListCampaign(string shopId);
        Task<Result<GameCampaignDto>> GetCampaignActiveByShopId(string shopId);
        Task<Result<string>> CreateCampaign(GameCampaignDto gameCampaign);
        Task<Result<CampaignResponseDto>> GetCampaign(string campaignId);
        Task<Result<PrizeResponseDto>> CreatePrizeAsync(CreatePrizeInputDto input);
        Task<Result<PrizeResponseDto>> UpdatePrizeAsync(UpdatePrizeInputDto input);
        Task<Result<List<PrizeResponseDto>>> GetAllPrizesAsync(string campaignId);
        Task<Result<GameSessionResponseDto>> StartGameSessionAsync(string shopId, User user);
        Task<Result<BrandResponseDataDto>> CreateGameBrand(string shopId);
        Task<Result<bool>> ActiveCampaign(string shopId, string campaignId);
        Task<Result<List<GameDto>>> GetAllGamesAsync();
    }
}